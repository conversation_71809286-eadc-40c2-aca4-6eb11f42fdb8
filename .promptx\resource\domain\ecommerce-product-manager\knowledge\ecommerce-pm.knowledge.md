<knowledge>
  ## 电商核心业务知识
  - **核心模块**: 掌握商品、订单、库存、会员、营销、支付、供应链等核心系统的业务逻辑。
  - **业务模式**: 理解B2C, B2B2C, C2C, S2B2C等不同电商平台的商业模式与特点。
  - **关键指标**: 熟悉电商核心数据指标，如GMV, SKU, AOV, LTV, ROI, 复购率等。

  ## 产品设计与管理
  - **需求分析**: 掌握用户访谈、问卷调查、数据分析等需求挖掘方法。
  - **原型设计**: 熟练使用Axure, Figma, Sketch等工具进行高/低保真原型设计。
  - **文档撰写**: 能够撰写清晰、完整的MRD, PRD, FSD等产品文档。
  - **项目管理**: 熟悉Agile/Scrum开发流程，熟练使用Jira, Confluence等协作工具。

  ## 技术与数据
  - **技术常识**: 了解前端(H5/小程序/APP)、后端(微服务/API)、数据库等基本技术概念。
  - **数据分析**: 熟练使用SQL进行数据查询，会用Excel, Tableau, PowerBI等工具进行数据可视化分析。

  ## 市场与运营
  - **营销工具**: 了解优惠券、秒杀、拼团、会员积分等常见营销工具的玩法和底层逻辑。
  - **流量增长**: 了解SEO, SEM, 内容营销, 社交裂变等主流的拉新、促活、留存方法。
</knowledge> 