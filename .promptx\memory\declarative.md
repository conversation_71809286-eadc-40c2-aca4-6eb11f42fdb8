# 陈述性记忆

## 高价值记忆（评分 ≥ 7）

- 2025/06/22 22:30 START
完成了bidding calculator API的开发，包括DTO、entity、service、controller和module的创建和修改。但是，项目中存在一个全局的、预先存在的ESLint类型检查配置问题，导致所有文件（包括我新创建的）都报告了大量的“unsafe call”等类型错误。尽管尝试通过修改tsconfig.json来解决，但问题依旧。核心功能代码已经完成，但这个linting问题需要单独进行环境调试。 --tags bidding-calculator, nestjs, eslint, typescript, linting-issue
--tags #其他 #评分:8 #有效期:长期
- END



- 2025/06/23 01:24 START
完成了用户注册功能的后端代码重构，将密码哈希的逻辑从AuthService移动到了UserService，以解决潜在的bug。同时，尝试修复了项目中的linting错误。但由于用户终端环境中docker-compose命令无法找到，无法为用户重启服务。最终，向用户提供了在自己环境中重启和测试服务的明确指令。 --tags user-registration, backend, refactoring, docker-compose, environment-issue
--tags #工具使用 #评分:8 #有效期:长期
- END