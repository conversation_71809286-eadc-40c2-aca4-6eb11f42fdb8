{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-06-26T03:10:27.336Z", "updatedAt": "2025-06-26T03:10:27.337Z", "resourceCount": 4}, "resources": [{"id": "ecommerce-product-manager", "source": "project", "protocol": "role", "name": "Ecommerce Product Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/ecommerce-product-manager/ecommerce-product-manager.role.md", "metadata": {"createdAt": "2025-06-26T03:10:27.337Z", "updatedAt": "2025-06-26T03:10:27.337Z", "scannedAt": "2025-06-26T03:10:27.337Z"}}, {"id": "ecommerce-pm", "source": "project", "protocol": "thought", "name": "Ecommerce Pm 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/ecommerce-product-manager/thought/ecommerce-pm.thought.md", "metadata": {"createdAt": "2025-06-26T03:10:27.337Z", "updatedAt": "2025-06-26T03:10:27.337Z", "scannedAt": "2025-06-26T03:10:27.337Z"}}, {"id": "ecommerce-pm", "source": "project", "protocol": "execution", "name": "Ecommerce Pm 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ecommerce-product-manager/execution/ecommerce-pm.execution.md", "metadata": {"createdAt": "2025-06-26T03:10:27.337Z", "updatedAt": "2025-06-26T03:10:27.337Z", "scannedAt": "2025-06-26T03:10:27.337Z"}}, {"id": "ecommerce-pm", "source": "project", "protocol": "knowledge", "name": "Ecommerce Pm 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ecommerce-product-manager/knowledge/ecommerce-pm.knowledge.md", "metadata": {"createdAt": "2025-06-26T03:10:27.337Z", "updatedAt": "2025-06-26T03:10:27.337Z", "scannedAt": "2025-06-26T03:10:27.337Z"}}], "stats": {"totalResources": 4, "byProtocol": {"role": 1, "thought": 1, "execution": 1, "knowledge": 1}, "bySource": {"project": 4}}}